# Environment files
.env
.env.*
!.env.example # Keep example file

# Dependency directories
node_modules/
*/node_modules

# Build directories
dist/
build/
.next/
out/

# Logs and databases
*.log
logs/
*.sqlite
*.sqlite3
backend.log
frontend.log

# Debug files
*.debug
debug/
audio-debug/

# Sensitive data
rec/
data/
conversations/

# IDE specific files
.vscode/
.idea/
*.swp
*.swo

# System files
.DS_Store
Thumbs.db

# Test coverage
coverage/
.nyc_output/

# Local development files
*.local

# Documentation and report files (keep only essential docs)
*_REPORT.md
*_AUDIT*.md
*_ANALYSIS.md
*_PLAN.md
*_SUMMARY.md
*_GUIDE.md
*_COOKBOOK.md
*_CHECKLIST.md
ACTION_PLAN.md
COMPREHENSIVE_*.md
WORKFLOW_*.md
SESSION_*.md
ERROR_*.md
AUDIO_*.md
CAMPAIGN_*.md
BUG_REPORT*.md
FEATURE_*.md
MIGRATION_*.md
RECOVERY_*.md
DEPLOYMENT.md
CONFIGURATION.md
FINAL_STATE.md
CLAUDE.md
AGENTS.md

# Test and debug files
test-*.js
test-*.sh
*-test.js
validate-*.js
verify-*.js
verify-*.sh
debug-*.js
check-*.js
quick-*.js

# Heritage and deployment files
docker-*.sh
deploy*.sh
*-deploy.sh
fix-*.sh
setup-*.sh
start*.sh
stop*.sh
run*.sh
health-*.sh
auto-*.sh
traefik.yml
nginx-*.conf
ecosystem.config.dev.*

# Data and result files
*_info.json
*-results*.json
validation-*.json
test-results*.json
*.pid

# Audio files
*.m4a
*.mp3
*.wav
intro.*

# HTML manager files
*-manager.html
*-scripts-manager.html
