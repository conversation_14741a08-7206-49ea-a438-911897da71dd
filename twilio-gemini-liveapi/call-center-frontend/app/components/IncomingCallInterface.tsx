import { cn } from '../../lib/utils';
interface IncomingCallInterfaceProps {
  audioMode: 'twilio' | 'local';
  isRecording: boolean;
  isProcessing: boolean;
  stopLocalTest: (index: number) => void;
  handleLocalTest: (company: {name: string; phone: string}, index: number) => void;
  fromPhoneNumber: string;
  incomingCallsEnabled: boolean;
  task: string;
}

export default function IncomingCallInterface({
  audioMode,
  isRecording,
  isProcessing,
  stopLocalTest,
  handleLocalTest,
  fromPhoneNumber,
  incomingCallsEnabled,
  task
}: IncomingCallInterfaceProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold mb-2 text-foreground">
        {audioMode === 'local' ? 'Local Audio Testing (Browser)' : 'Incoming Call Testing'}
      </h3>
      {audioMode === 'local' ? (
        <div className="border border-border rounded-lg p-4 bg-card">
          <div className="text-center space-y-4">
            <p className="text-sm text-muted-foreground">
              Test incoming call scripts using your browser microphone and speakers
            </p>
            <div className="flex justify-center gap-4">
              <button
                type="button"
                onClick={isRecording ? () => stopLocalTest(0) : () => handleLocalTest({ name: 'Local Test', phone: 'N/A' }, 0)}
                disabled={isProcessing}
                className={cn(
                  'px-6 py-3 rounded-md font-medium transition-colors',
                  isRecording ? 'bg-red-600 text-white hover:bg-red-700' : 'bg-green-600 text-white hover:bg-green-700',
                  'disabled:opacity-50 disabled:cursor-not-allowed'
                )}
              >
                {isRecording ? 'Stop Test' : 'Start Test'}
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="border border-border rounded-lg p-4 bg-card">
          <div className="text-center space-y-4">
            <p className="text-sm text-muted-foreground">
              Incoming calls will be handled automatically when enabled.
              Call your Twilio number: <strong>{fromPhoneNumber}</strong>
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <p className="text-sm text-blue-800">
                <strong>Status:</strong> {incomingCallsEnabled ? 'Ready to receive calls' : 'Incoming calls disabled'}
              </p>
              {incomingCallsEnabled && (
                <p className="text-xs text-blue-600 mt-1">
                  Using campaign script: {task ? 'Custom script loaded' : 'Default Campaign 1'}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
