import { Modality } from '../gemini/client';
import { endSession } from './session-utils';
import { globalHeartbeatManager } from './heartbeat-manager';
import type {
    WebSocketConnection,
    FlowDependencies,
    ConnectionData,
    SessionData,
    LocalStartMessage
} from '../types/websocket';
import type { WebSocket } from 'ws';

export async function handleStartSession(
    sessionId: string,
    data: LocalStartMessage,
    deps: FlowDependencies,
    connection: WebSocketConnection,
    ws: WebSocket,
    flowType: string,
    isIncomingCall: boolean,
    getSessionConfig: (callSid?: string) => any,
    activeConnections: Map<string, ConnectionData>,
    healthMonitor: any,
    lifecycleManager: any,
    sessionManager: any
): Promise<SessionData> {
    try {
        let sessionConfig = await getSessionConfig(sessionId);

        if (data.aiInstructions) {sessionConfig.aiInstructions = data.aiInstructions;}
        if (data.voice) {sessionConfig.voice = deps.voiceManager.getValidGeminiVoice(data.voice);}
        if (data.model) {sessionConfig.model = deps.modelManager.getValidGeminiModel(data.model);}
        if (data.scriptId) {
            const testConfig = await deps.scriptManager.getScriptConfig(data.scriptId, isIncomingCall);
            if (testConfig) {
                sessionConfig = {
                    ...testConfig,
                    aiInstructions: `[TESTING MODE] ${testConfig.aiInstructions}`,
                    isTestMode: true
                };
            }
        }

        // CRITICAL VALIDATION: Ensure AI instructions are present before creating session
        if (!sessionConfig || !sessionConfig.aiInstructions || sessionConfig.aiInstructions.trim().length === 0) {
            const errorMessage = `No AI instructions available for ${flowType} session. Cannot create Gemini session without proper system instructions.`;
            console.error(`❌ [${sessionId}] VALIDATION FAILED: ${errorMessage}`);
            console.error(`❌ [${sessionId}] Session config validation:`, {
                hasConfig: !!sessionConfig,
                hasAiInstructions: !!sessionConfig?.aiInstructions,
                instructionLength: sessionConfig?.aiInstructions?.length || 0,
                flowType: flowType,
                scriptId: sessionConfig?.scriptId,
                isTestMode: sessionConfig?.isTestMode
            });
            
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-error',
                    error: errorMessage,
                    code: 'MISSING_AI_INSTRUCTIONS'
                }));
            }
            
            // Clean up and return early
            activeConnections.delete(sessionId);
            return { geminiSession: null, isSessionActive: false };
        }

        console.log(`✅ [${sessionId}] AI instructions validation passed (${sessionConfig.aiInstructions.length} characters)`);
        const instructionPreview = sessionConfig.aiInstructions.substring(0, 150) + (sessionConfig.aiInstructions.length > 150 ? '...' : '');
        console.log(`🎯 [${sessionId}] Instructions preview: ${instructionPreview}`);

        const connectionData = createConnectionData(sessionId, connection, sessionConfig, flowType, isIncomingCall);
        activeConnections.set(sessionId, connectionData);

        healthMonitor.trackConnection(sessionId, 'connected', {
            flowType,
            isTestMode: true,
            scriptId: sessionConfig.scriptId
        });

        // Use SessionManager for consistent session creation
        // Determine if this is an incoming call based on session config or query parameters
        const detectedIsIncomingCall = sessionConfig.isIncomingCall ||
                              connection.query?.type === 'incoming' ||
                              connection.query?.flow === 'inbound' ||
                              sessionConfig.scriptType === 'incoming';

        const geminiSession = await deps.sessionManager.createGeminiSession(sessionId, {
            ...sessionConfig,
            sessionType: 'local_test',
            isIncomingCall: detectedIsIncomingCall // Properly detect incoming vs outbound testing
        }, connectionData);

        connectionData.geminiSession = geminiSession || undefined;

        let isSessionActive = false;

        if (geminiSession) {
            isSessionActive = true;
            await initTranscription(sessionId, deps, connectionData);
            if (ws.readyState === 1) {
                ws.send(
                    JSON.stringify({
                        type: 'session-started',
                        sessionId,
                        flowType,
                        scriptId: sessionConfig.scriptId,
                        config: {
                            voice: sessionConfig.voice,
                            model: sessionConfig.model,
                            isIncomingCall: detectedIsIncomingCall,
                            transcriptionEnabled: !!connectionData.deepgramConnection
                        }
                    })
                );
            }
        }

        return { geminiSession, isSessionActive };
    } catch (error) {
        if (ws.readyState === 1) {
            ws.send(
                JSON.stringify({ type: 'session-error', error: `Session start failed: ${(error as Error).message}` })
            );
        }
        return { geminiSession: null, isSessionActive: false };
    }
}

function createConnectionData(
    sessionId: string, 
    connection: WebSocketConnection, 
    sessionConfig: any, 
    flowType: string, 
    isIncomingCall: boolean
): ConnectionData {
    return {
        localWs: (connection.socket || connection) as WebSocket,
        sessionId,
        isSessionActive: false,
        summaryRequested: false,
        summaryReceived: false,
        summaryText: '',
        conversationLog: [],
        fullTranscript: [],
        speechTranscript: [],
        isIncomingCall,
        sessionType: 'local_test',
        flowType,
        sessionStartTime: Date.now(),
        lastActivity: Date.now(),
        targetName: sessionConfig.targetName || 'Test Contact',
        targetPhoneNumber: sessionConfig.targetPhoneNumber || '+1234567890',
        originalAIInstructions: sessionConfig.aiInstructions,
        scriptId: sessionConfig.scriptId,
        isTestMode: true,
        lastAIResponse: Date.now(),
        responseTimeouts: 0,
        connectionQuality: 'good',
        lastContextSave: Date.now(),
        contextSaveInterval: null
    };
}

// createGeminiSession function removed - now using SessionManager.createGeminiSession() for consistency

function handleGeminiMessage(
    sessionId: string, 
    message: any, 
    deps: FlowDependencies, 
    connectionData: ConnectionData
): void {
    console.log(`🔍 [${sessionId}] handleGeminiMessage called - message keys:`, Object.keys(message || {}));

    if (message.setupComplete || message.goAway) {
        console.log(`🔍 [${sessionId}] Skipping setupComplete or goAway message`);
        return;
    }

    const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
    const text = message.serverContent?.modelTurn?.parts?.[0]?.text;

    console.log(`🔍 [${sessionId}] Audio check: audio exists: ${!!audio}, data length: ${audio?.data?.length || 0}`);

    // ENHANCED AUDIO HANDLING - Match working local testing handler
    if (audio && audio.data && audio.data.length > 0) {
        // Get fresh connection data to ensure we have the latest WebSocket state
        const freshConnectionData = deps.activeConnections?.get(sessionId) || connectionData;
        const localWs = freshConnectionData?.localWs;

        console.log(`🔍 [${sessionId}] Audio forwarding check: audio=${!!audio}, data=${audio.data?.length || 0}, localWs=${!!localWs}, readyState=${localWs?.readyState}`);

        if (localWs && localWs.readyState === 1) { // WebSocket.OPEN
            console.log(`🔊 [${sessionId}] Sending audio response to client, size: ${audio.data?.length || 0}, mimeType: ${audio.mimeType}`);

            try {
                // Send audio back to client with metadata for proper playback
                localWs.send(JSON.stringify({
                    type: 'audio',
                    audio: audio.data,
                    mimeType: audio.mimeType // Include mime type for sample rate info
                }));
                console.log(`✅ [${sessionId}] Audio sent successfully to WebSocket`);
            } catch (sendError) {
                console.error(`❌ [${sessionId}] Error sending audio to WebSocket:`, sendError);
                console.error(`🔍 [${sessionId}] WebSocket state after error: ${localWs.readyState}`);
            }
        } else {
            console.warn(`⚠️ [${sessionId}] Cannot send audio: localWs=${!!localWs}, readyState=${localWs?.readyState}`);
        }
    }

    if (text) {
        console.log(`💬 [${sessionId}] Text response received: ${text.substring(0, 100)}...`);
        connectionData.lastAIResponse = Date.now();
        connectionData.responseTimeouts = 0;
        connectionData.connectionQuality = 'good';
    }
}

async function initTranscription(
    sessionId: string, 
    deps: FlowDependencies, 
    connectionData: ConnectionData
): Promise<void> {
    try {
        const dgConnection = await deps.transcriptionManager.initializeLocalTranscription(sessionId, connectionData as any);
        if (dgConnection) {
            connectionData.deepgramConnection = dgConnection as any;
        }
    } catch (error) {
        // Silently handle Deepgram connection errors
        console.warn('Deepgram connection failed:', error);
    }
}