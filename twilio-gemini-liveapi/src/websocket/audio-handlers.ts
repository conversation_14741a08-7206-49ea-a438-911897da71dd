import type { 
  ConnectionData, 
  WebSocketDependencies, 
  LocalAudioMessage,
  LocalTextMessage
} from '../types/websocket';
import { LifecycleManager } from '../session/lifecycle-manager';
import { RecoveryManager } from '../session/recovery-manager';

export async function handleAudioData(
    sessionId: string,
    data: LocalAudioMessage,
    geminiSession: any,
    isSessionActive: boolean,
    deps: WebSocketDependencies,
    activeConnections: Map<string, ConnectionData>,
    lifecycleManager: LifecycleManager,
    recoveryManager: RecoveryManager,
    flowType: string
): Promise<void> {
    if (geminiSession && isSessionActive && (data.audio || data.audioData)) {
        try {
            // Update activity by transitioning to active state if not already
            lifecycleManager.transitionState(sessionId, 'active', 'audio_received');
            // Standardize on 'audio' field name - prefer 'audio' over legacy 'audioData'
            const base64Audio = data.audio || data.audioData || '';
            await deps.sessionManager.sendBrowserAudioToGemini(sessionId, geminiSession, base64Audio);
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && connectionData.deepgramConnection && deps.transcriptionManager) {
                try {
                    const audioBuffer = Buffer.from(base64Audio, 'base64');
                    deps.transcriptionManager.sendAudioToTranscription(sessionId, audioBuffer);
                } catch (transcriptionError) {
                    console.error(`❌ [${sessionId}] Error sending audio to transcription:`, transcriptionError);
                }
            }
        } catch (error) {
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(sessionId, activeConnections)) {
                await recoveryManager.recoverSession(sessionId, 'audio_processing_error', activeConnections);
            }
        }
    }
}

export async function handleTextMessage(
    sessionId: string, 
    data: LocalTextMessage, 
    geminiSession: any, 
    isSessionActive: boolean, 
    deps: WebSocketDependencies
): Promise<void> {
    if (geminiSession && isSessionActive && data.text) {
        try {
            await deps.sessionManager.sendTextToGemini(sessionId, geminiSession, data.text);
        } catch (textError) {
            console.error(`❌ [${sessionId}] Error sending text to Gemini:`, textError);
        }
    }
}

export async function handleTurnComplete(
    sessionId: string, 
    geminiSession: any, 
    isSessionActive: boolean, 
    deps: WebSocketDependencies
): Promise<void> {
    if (geminiSession && isSessionActive) {
        try {
            await deps.sessionManager.sendTurnComplete(sessionId, geminiSession);
        } catch (turnCompleteError) {
            console.error(`❌ [${sessionId}] Error sending turn complete to Gemini:`, turnCompleteError);
        }
    }
}