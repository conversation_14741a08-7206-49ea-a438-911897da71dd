// WebSocket helper functions to reduce complexity in handlers.ts
import { FastifyInstance } from 'fastify';
import type { WebSocketConnection, FlowDependencies } from '../types/websocket';
import { authLogger } from '../utils/logger';
import { timingSafeEqual } from 'crypto';

export interface WebSocketEndpointConfig {
    path: string;
    handler: (connection: WebSocketConnection, deps: FlowDependencies) => void;
    callType: 'inbound' | 'outbound';
    isTestMode: boolean;
    logMessage?: string;
}

/**
 * Validates WebSocket authentication
 */
export function validateWebSocketAuth(req: any): boolean {
    // Skip auth for Twilio webhooks (they use signature validation)
    const isTwilioWebhook = req.headers['user-agent']?.includes('TwilioProxy') ||
                           req.headers['x-twilio-signature'];

    if (isTwilioWebhook) {
        authLogger.info('WebSocket: Allowing Twilio webhook connection');
        return true;
    }

    // For non-Twilio connections, require authentication in production only when forced
    if (process.env.NODE_ENV === 'production' && process.env.FORCE_AUTH === 'true') {
        const authHeader = req.headers.authorization;

        if (!authHeader) {
            authLogger.error('WebSocket: No authorization header in production', {
                url: req.url,
                ip: req.socket?.remoteAddress
            });
            return false;
        }

        try {
            const token = authHeader.replace('Bearer ', '');

            if (!token || token === 'undefined' || token === 'null') {
                authLogger.error('WebSocket: Invalid token format');
                return false;
            }

            // Validate token format
            if (token.length < 32) {
                authLogger.error('WebSocket: Token too short');
                return false;
            }

            // Check against configured API key
            const validApiKey = process.env.API_KEY || process.env.SUPABASE_SERVICE_KEY || process.env.GEMINI_API_KEY || process.env.TWILIO_AUTH_TOKEN;
            if (!validApiKey) {
                authLogger.error('WebSocket: No API key configured for production');
                return false;
            }

            // Ensure both tokens are the same length to prevent timing attacks
            if (token.length !== validApiKey.length) {
                authLogger.error('WebSocket: Invalid API key length');
                return false;
            }

            // Use timing-safe comparison
            if (!timingSafeEqual(Buffer.from(token), Buffer.from(validApiKey))) {
                authLogger.error('WebSocket: Invalid API key');
                return false;
            }

            authLogger.info('WebSocket: Authentication successful');
            return true;

        } catch (error) {
            authLogger.error('WebSocket: Auth validation failed', error instanceof Error ? error : new Error(String(error)));
            return false;
        }
    }

    // Allow in development and test environments with warning
    if (process.env.NODE_ENV === 'test') {
        authLogger.debug('WebSocket: No authentication required for test environment');
    } else {
        authLogger.warn('WebSocket: No authentication required for development environment');
    }

    return true;
}

/**
 * Handles WebSocket authentication failure
 */
export function handleAuthFailure(connection: any, endpoint: string): void {
    authLogger.error(`WebSocket: Authentication failed for ${endpoint}`);
    connection.socket.close(1008, 'Authentication required');
}

/**
 * Creates enhanced dependencies for WebSocket handlers
 */
export function createEnhancedDependencies(
    baseDependencies: any,
    callType: 'inbound' | 'outbound',
    isTestMode: boolean
): FlowDependencies {
    return {
        ...baseDependencies,
        callType,
        isTestMode
    } as FlowDependencies;
}

/**
 * Registers a WebSocket endpoint with authentication and error handling
 */
export function registerWebSocketEndpoint(
    fastify: FastifyInstance,
    config: WebSocketEndpointConfig,
    baseDependencies: any
): void {
    fastify.register(async (fastify) => {
        fastify.get(config.path, { websocket: true }, (connection: any, req: any) => {
            // Log connection if specified
            if (config.logMessage) {
                console.log(config.logMessage);
                console.log('🔌 WebSocket headers:', req.headers);
            }

            // CRITICAL FIX: Extract query parameters from URL
            const url = new URL(req.url, `http://${req.headers.host}`);
            const queryParams = Object.fromEntries(url.searchParams.entries());
            const callSid = queryParams.CallSid || queryParams.callSid;
            
            if (callSid) {
                console.log(`🆔 [${callSid}] CallSid extracted from WebSocket URL: ${req.url}`);
            } else {
                console.log(`⚠️ No CallSid found in WebSocket URL: ${req.url}`);
                console.log(`🔍 Available query params:`, queryParams);
            }

            // Validate authentication
            if (!validateWebSocketAuth(req)) {
                handleAuthFailure(connection, config.path);
                return;
            }

            // Create enhanced dependencies with query parameters
            const enhancedDeps = createEnhancedDependencies(
                baseDependencies,
                config.callType,
                config.isTestMode
            );

            // Attach query parameters and CallSid to connection for handler access
            (connection as any).queryParams = queryParams;
            (connection as any).callSid = callSid;

            // Call the handler
            config.handler(connection, enhancedDeps);
        });
    });
}

/**
 * Registers multiple WebSocket endpoints efficiently
 */
export function registerMultipleWebSocketEndpoints(
    fastify: FastifyInstance,
    configs: WebSocketEndpointConfig[],
    baseDependencies: any
): void {
    configs.forEach(config => {
        registerWebSocketEndpoint(fastify, config, baseDependencies);
    });
}

/**
 * Creates standard WebSocket endpoint configurations
 */
export function createStandardEndpointConfigs(handlers: {
    handleOutboundCall: (connection: WebSocketConnection, deps: FlowDependencies) => void;
    handleInboundCall: (connection: WebSocketConnection, deps: FlowDependencies) => void;
    handleOutboundTesting: (connection: WebSocketConnection, deps: FlowDependencies) => void;
    handleInboundTesting: (connection: WebSocketConnection, deps: FlowDependencies) => void;
}): WebSocketEndpointConfig[] {
    return [
        {
            path: '/media-stream',
            handler: handlers.handleOutboundCall,
            callType: 'outbound',
            isTestMode: false,
            logMessage: '🔌 WebSocket connection received on /media-stream'
        },
        {
            path: '/media-stream-inbound',
            handler: handlers.handleInboundCall,
            callType: 'inbound',
            isTestMode: false,
            logMessage: '🔌 WebSocket connection received on /media-stream-inbound'
        },
        {
            path: '/test-outbound',
            handler: handlers.handleOutboundTesting,
            callType: 'outbound',
            isTestMode: true
        },
        {
            path: '/test-inbound',
            handler: handlers.handleInboundTesting,
            callType: 'inbound',
            isTestMode: true
        },
        {
            path: '/local-audio-session',
            handler: handlers.handleOutboundTesting,
            callType: 'outbound',
            isTestMode: true
        },
        {
            path: '/gemini-live',
            handler: handlers.handleOutboundTesting,
            callType: 'outbound',
            isTestMode: true,
            logMessage: '🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection'
        }
    ];
}

/**
 * Logs WebSocket connection details
 */
export function logWebSocketConnection(
    callType: 'inbound' | 'outbound',
    isTestMode: boolean,
    additionalInfo?: string
): void {
    const mode = isTestMode ? 'TEST' : 'CALL';
    const type = callType.toUpperCase();
    const info = additionalInfo ? ` - ${additionalInfo}` : '';
    console.log(`📞 [${type} ${mode}] Client connected${info}`);
}

/**
 * Creates flow dependencies with proper typing
 */
export function createFlowDependencies(
    baseDeps: any,
    overrides: Partial<FlowDependencies>
): FlowDependencies {
    return {
        ...baseDeps,
        ...overrides
    } as FlowDependencies;
}
