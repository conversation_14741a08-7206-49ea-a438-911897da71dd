/**
 * Audio Forwarding Utilities
 * Handles proper audio forwarding with sequence numbers for Twilio calls
 * Fixes the missing audio forwarding issues identified in the audit
 */

import { getTwilioWebSocket, getLocalWebSocket, safeSendWebSocket } from '../utils/websocket-utils';
import { logger } from '../utils/logger';
import { AudioProcessor } from './audio-processor';
import { ConnectionData } from '../types/global';

// Type definitions
interface AudioData {
    data: string;
    mimeType?: string;
}

interface AudioMessage {
    type: string;
    audio: string;
    mimeType: string;
    timestamp: number;
    sessionId: string;
}

interface TwilioMediaMessage {
    event: string;
    sequenceNumber: string;
    streamSid: string;
    media: {
        payload: string;
    };
}

interface AudioForwardingStats {
    sessionId: string;
    sessionType?: string;
    audioForwardingEnabled: boolean;
    lastAudioSent: number;
    timeSinceLastAudio: number | null;
    sequenceNumber?: number;
    streamSid?: string | null;
    hasTwilioWs?: boolean;
    hasLocalWs?: boolean;
}

/**
 * Forward audio to Twilio WebSocket with proper sequence number handling
 */
export async function forwardAudioToTwilio(
    sessionId: string, 
    audio: AudioData, 
    connectionData: ConnectionData, 
    audioProcessor: AudioProcessor
): Promise<boolean> {
    try {
        if (!audio || !audio.data) {
            console.warn(`⚠️ [${sessionId}] No audio data to forward to Twilio`);
            return false;
        }

        const twilioWs = getTwilioWebSocket(connectionData);
        if (!twilioWs) {
            console.warn(`⚠️ [${sessionId}] No Twilio WebSocket available for audio forwarding`);
            return false;
        }

        if (!connectionData.streamSid) {
            console.warn(`⚠️ [${sessionId}] No streamSid available for Twilio audio forwarding`);
            return false;
        }

        // Convert Gemini PCM audio to μ-law format for Twilio
        // Gemini returns base64-encoded PCM audio at 24kHz, we need to convert to μ-law at 8kHz
        let convertedAudio: string | Buffer;
        let convertedAudioBase64: string;

        try {
            // Try the main conversion method first
            convertedAudio = audioProcessor?.convertPCMToUlaw?.(audio.data);

            if (!convertedAudio) {
                // Try fallback method
                convertedAudio = audioProcessor?.fallbackPCMToUlaw?.(audio.data);
            }

            if (!convertedAudio) {
                console.error(`❌ [${sessionId}] Both convertPCMToUlaw methods failed`);
                console.error(`❌ [${sessionId}] Audio data type: ${typeof audio.data}, length: ${audio.data?.length || 0}`);
                console.error(`❌ [${sessionId}] Audio mimeType: ${audio.mimeType}`);
                return false;
            }

            // Convert Buffer to base64 string if needed for Twilio transmission
            convertedAudioBase64 = Buffer.isBuffer(convertedAudio) 
                ? convertedAudio.toString('base64') 
                : convertedAudio;

            console.log(`🔄 [${sessionId}] Audio conversion successful: ${convertedAudioBase64.length} bytes`);

        } catch (conversionError) {
            console.error(`❌ [${sessionId}] Audio conversion error:`, conversionError);
            return false;
        }

        // Initialize sequence number if not exists
        if (typeof connectionData.sequenceNumber !== 'number') {
            connectionData.sequenceNumber = 0;
            console.log(`🔢 [${sessionId}] Initialized Twilio sequence number to 0`);
        }

        // Create Twilio media message with sequence number
        const audioDelta: TwilioMediaMessage = {
            event: 'media',
            sequenceNumber: connectionData.sequenceNumber.toString(),
            streamSid: connectionData.streamSid,
            media: {
                payload: convertedAudioBase64
            }
        };

        // Send to Twilio WebSocket
        const success = safeSendWebSocket(twilioWs, audioDelta, sessionId);
        
        if (success) {
            // Increment sequence number for next packet with overflow protection
            connectionData.sequenceNumber = (connectionData.sequenceNumber + 1) % Number.MAX_SAFE_INTEGER;
            connectionData.lastAudioSent = Date.now();
            
            console.log(`✅ [${sessionId}] Audio sent to Twilio (seq: ${connectionData.sequenceNumber - 1}, size: ${convertedAudioBase64.length})`);
            
            // Log sequence number every 10 packets for debugging
            if (connectionData.sequenceNumber % 10 === 0) {
                console.log(`📊 [${sessionId}] Twilio audio stats: ${connectionData.sequenceNumber} packets sent`);
            }
            
            return true;
        } else {
            console.error(`❌ [${sessionId}] Failed to send audio to Twilio WebSocket`);
            return false;
        }

    } catch (error) {
        console.error(`❌ [${sessionId}] Error forwarding audio to Twilio:`, error);
        return false;
    }
}

/**
 * Forward audio to local testing WebSocket
 */
export async function forwardAudioToLocal(
    sessionId: string, 
    audio: AudioData, 
    connectionData: ConnectionData
): Promise<boolean> {
    try {
        if (!audio || !audio.data) {
            console.warn(`⚠️ [${sessionId}] No audio data to forward to local WebSocket`);
            return false;
        }

        const localWs = getLocalWebSocket(connectionData);
        if (!localWs) {
            console.warn(`⚠️ [${sessionId}] No local WebSocket available for audio forwarding`);
            return false;
        }

        // For local testing, send audio data with proper format information
        const audioMessage: AudioMessage = {
            type: 'audio',
            audio: audio.data, // Use standardized 'audio' field name
            mimeType: audio.mimeType || 'audio/pcm;rate=24000', // Include mime type for proper playback
            timestamp: Date.now(),
            sessionId: sessionId
        };

        const success = safeSendWebSocket(localWs, audioMessage, sessionId);
        
        if (success) {
            connectionData.lastAudioSent = Date.now();
            console.log(`✅ [${sessionId}] Audio sent to local WebSocket (size: ${audio.data.length})`);
            return true;
        } else {
            console.error(`❌ [${sessionId}] Failed to send audio to local WebSocket`);
            return false;
        }

    } catch (error) {
        console.error(`❌ [${sessionId}] Error forwarding audio to local WebSocket:`, error);
        return false;
    }
}

/**
 * Main audio forwarding function that routes to appropriate destination
 */
export async function forwardAudio(
    sessionId: string, 
    audio: AudioData, 
    connectionData: ConnectionData, 
    audioProcessor: AudioProcessor
): Promise<boolean> {
    if (!audio || !connectionData) {
        console.warn(`⚠️ [${sessionId}] Missing audio or connection data for forwarding`);
        return false;
    }

    // Determine session type and route accordingly
    const sessionType = connectionData.sessionType || 'unknown';
    const isTwilioCall = connectionData.isTwilioCall || sessionType === 'twilio_call';
    
    console.log(`🔍 [${sessionId}] Audio forwarding: sessionType=${sessionType}, isTwilioCall=${isTwilioCall}`);

    if (isTwilioCall) {
        return await forwardAudioToTwilio(sessionId, audio, connectionData, audioProcessor);
    } else {
        return await forwardAudioToLocal(sessionId, audio, connectionData);
    }
}

/**
 * Initialize audio forwarding for a session
 */
export function initializeAudioForwarding(
    sessionId: string, 
    connectionData: ConnectionData, 
    sessionType: string
): void {
    if (!connectionData) {
        console.warn(`⚠️ [${sessionId}] No connection data for audio forwarding initialization`);
        return;
    }

    // Set audio forwarding properties based on session type
    if (sessionType === 'twilio_call' || connectionData.isTwilioCall) {
        connectionData.audioForwardingEnabled = true;
        connectionData.sequenceNumber = 0;
        connectionData.lastAudioSent = 0;
        console.log(`🎵 [${sessionId}] Initialized Twilio audio forwarding`);
    } else {
        connectionData.audioForwardingEnabled = true;
        connectionData.lastAudioSent = 0;
        console.log(`🎵 [${sessionId}] Initialized local audio forwarding`);
    }
}

/**
 * Get audio forwarding statistics
 */
export function getAudioForwardingStats(
    sessionId: string, 
    connectionData: ConnectionData
): AudioForwardingStats | null {
    if (!connectionData) {
        return null;
    }

    const stats: AudioForwardingStats = {
        sessionId,
        sessionType: connectionData.sessionType,
        audioForwardingEnabled: connectionData.audioForwardingEnabled || false,
        lastAudioSent: connectionData.lastAudioSent || 0,
        timeSinceLastAudio: connectionData.lastAudioSent ? Date.now() - connectionData.lastAudioSent : null
    };

    // Add Twilio-specific stats
    if (connectionData.isTwilioCall || connectionData.sessionType === 'twilio_call') {
        stats.sequenceNumber = connectionData.sequenceNumber || 0;
        stats.streamSid = connectionData.streamSid || null;
        stats.hasTwilioWs = !!(connectionData.twilioWs || connectionData.ws);
    } else {
        stats.hasLocalWs = !!(connectionData.localWs || connectionData.ws);
    }

    return stats;
}

/**
 * Cleanup audio forwarding resources
 */
export function cleanupAudioForwarding(
    sessionId: string, 
    connectionData: ConnectionData
): void {
    if (!connectionData) {
        return;
    }

    // Reset audio forwarding state
    connectionData.audioForwardingEnabled = false;
    connectionData.sequenceNumber = 0;
    connectionData.lastAudioSent = 0;
    
    console.log(`🧹 [${sessionId}] Audio forwarding cleanup completed`);
}