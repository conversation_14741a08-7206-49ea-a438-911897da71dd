// Call handling utilities for breaking down complex route handlers
import { FastifyRequest, FastifyReply } from 'fastify';
import { CallConfig } from '../types/api-types';

export interface IncomingCallBody {
    CallSid: string;
    From: string;
    To: string;
    CallStatus: string;
    Direction: string;
}

export interface CallValidationResult {
    isValid: boolean;
    error?: string;
    statusCode?: number;
}

export interface ScriptConfigResult {
    config: CallConfig | null;
    error?: string;
}

export interface WebSocketUrlResult {
    url: string;
    error?: string;
}

/**
 * Validates the incoming call request
 */
export function validateIncomingCall(body: IncomingCallBody): CallValidationResult {
    if (!body.CallSid) {
        return {
            isValid: false,
            error: 'Missing CallSid',
            statusCode: 400
        };
    }

    return { isValid: true };
}

/**
 * Determines if the call is outbound based on direction
 */
export function isOutboundCall(direction: string): boolean {
    return direction === 'outbound-api' || direction === 'outbound-dial';
}

/**
 * Gets the outbound call script configuration
 */
export function getOutboundScriptConfig(nextCallConfig: any): CallConfig {
    return {
        ...nextCallConfig,
        scriptType: 'outbound'
    };
}

/**
 * Gets the inbound call script configuration with fallbacks
 */
export function getInboundScriptConfig(
    scriptManager: any,
    from: string
): ScriptConfigResult {
    try {
        // Try to get current incoming script
        const currentScript = scriptManager.getCurrentIncomingScript();
        if (currentScript && currentScript.id) {
            console.log(`🔍 [INBOUND] Found current script: ${currentScript.id}`);
            const tempConfig = scriptManager.getScriptConfig(currentScript.id, true);
            if (tempConfig && tempConfig.aiInstructions) {
                console.log(`✅ [INBOUND] Using current incoming script: ${currentScript.id}`);
                return { config: tempConfig as CallConfig };
            } else {
                console.warn(`⚠️ [INBOUND] Current script ${currentScript.id} has no instructions`);
            }
        } else {
            console.log(`🔍 [INBOUND] No current script found, trying default`);
        }

        // Fallback: Try to load default incoming campaign script
        try {
            console.log(`🔧 [INBOUND] Loading default incoming campaign script 1 (ID: 7)`);
            const tempConfig = scriptManager.getScriptConfig(7, true); // ID 7 = incoming campaign 1
            if (tempConfig && tempConfig.aiInstructions) {
                console.log(`✅ [INBOUND] Using default incoming campaign script 1`);
                return { config: tempConfig as CallConfig };
            } else {
                throw new Error('Default script has no instructions');
            }
        } catch (defaultError) {
            console.error('❌ [INBOUND] Error loading default script:', defaultError);
        }

        // Last resort fallback
        console.warn('⚠️ [INBOUND] Using minimal fallback config');
        const fallbackConfig: CallConfig = {
            aiInstructions: 'You are a helpful customer service representative. Greet the caller warmly and ask how you can help them today.',
            voice: process.env.GEMINI_DEFAULT_VOICE || 'Kore',
            model: process.env.GEMINI_DEFAULT_MODEL || 'gemini-2.5-flash-preview-native-audio-dialog',
            scriptType: 'inbound',
            scriptId: 'customer-service',
            targetName: null,
            targetPhoneNumber: null,
            phoneNumber: from || '',
            mode: 'inbound'
        };

        return { config: fallbackConfig };

    } catch (error) {
        console.error('❌ [INBOUND] Error getting script config:', error);
        
        const errorFallbackConfig: CallConfig = {
            aiInstructions: 'You are a helpful customer service representative. Greet the caller warmly and ask how you can help them today.',
            voice: 'Kore',
            model: 'gemini-2.0-flash-exp',
            scriptType: 'inbound',
            scriptId: 'customer-service',
            targetName: null,
            targetPhoneNumber: null,
            phoneNumber: from || '',
            mode: 'inbound'
        };

        return { config: errorFallbackConfig };
    }
}

/**
 * Creates the final call configuration object
 */
export function createCallConfig(
    scriptConfig: CallConfig,
    callData: IncomingCallBody,
    isOutbound: boolean
): CallConfig {
    return {
        ...scriptConfig,
        callSid: callData.CallSid,
        from: callData.From,
        to: callData.To,
        callStatus: callData.CallStatus,
        isIncomingCall: !isOutbound,
        timestamp: new Date().toISOString()
    };
}

/**
 * Constructs the WebSocket URL for the call
 */
export function constructWebSocketUrl(
    request: FastifyRequest,
    isOutbound: boolean,
    publicUrl?: string
): WebSocketUrlResult {
    const streamUrl = isOutbound ? '/media-stream' : '/media-stream-inbound';
    
    let wsUrl: string;
    if (publicUrl) {
        // Convert HTTP(S) to WS(S) protocol
        const publicUrlBase = publicUrl.replace(/^https?:/, (match) => {
            return match === 'https:' ? 'wss:' : 'ws:';
        });
        wsUrl = `${publicUrlBase}${streamUrl}`;
    } else {
        // Fallback to request headers if PUBLIC_URL not set
        const protocol = request.protocol === 'https' ? 'wss' : 'ws';
        wsUrl = `${protocol}://${request.headers.host}${streamUrl}`;
    }
    
    // Validate WebSocket URL format
    if (!wsUrl.startsWith('ws://') && !wsUrl.startsWith('wss://')) {
        return {
            url: '',
            error: 'Invalid WebSocket URL format'
        };
    }

    return { url: wsUrl };
}

/**
 * Escapes URL for XML safety
 */
export function escapeXmlUrl(url: string): string {
    return url.replace(/&/g, '&amp;')
              .replace(/</g, '&lt;')
              .replace(/>/g, '&gt;')
              .replace(/"/g, '&quot;')
              .replace(/'/g, '&apos;');
}

/**
 * Generates TwiML response for connecting to WebSocket
 */
export function generateTwiML(escapedWsUrl: string): string {
    return `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Connect>
        <Stream url="${escapedWsUrl}" />
    </Connect>
    <Pause length="3600" />
</Response>`;
}

/**
 * Generates error TwiML response
 */
export function generateErrorTwiML(): string {
    return `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="alice">Sorry, we're experiencing technical difficulties. Please try again later.</Say>
    <Hangup />
</Response>`;
}

/**
 * Logs call configuration details
 */
export function logCallConfiguration(
    callConfig: CallConfig,
    scriptConfig: CallConfig,
    isOutbound: boolean
): void {
    const callType = isOutbound ? 'OUTBOUND' : 'INBOUND';
    
    console.log(`🔍 [${callType}] Call config stored:`, {
        callSid: callConfig.callSid,
        hasInstructions: !!callConfig.aiInstructions,
        instructionsLength: callConfig.aiInstructions?.length || 0,
        voice: callConfig.voice,
        model: callConfig.model,
        scriptId: callConfig.scriptId,
        isIncomingCall: callConfig.isIncomingCall
    });

    console.log(`✅ [${callType}] Call ${callConfig.callSid} configured with script: ${scriptConfig.scriptId}`);
    console.log(`📞 [${callConfig.callSid}] Call Details:`, {
        from: callConfig.from,
        to: callConfig.to,
        direction: (callConfig as any).direction,
        status: callConfig.callStatus,
        scriptType: scriptConfig.scriptType,
        hasInstructions: !!scriptConfig.aiInstructions,
        voice: scriptConfig.voice,
        model: scriptConfig.model
    });
}
