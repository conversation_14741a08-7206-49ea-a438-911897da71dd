// Campaign Configuration System
// Replaces hardcoded campaign scripts with configurable system

import { readFileSync, existsSync } from 'fs';
import path from 'path';
import { config, getConfigValue } from './config';
import { configLogger as logger } from '../utils/logger';
import { createClient } from '@supabase/supabase-js';
import type { CampaignScript } from '../types/global';

interface CachedScript {
    script: CampaignScript;
    timestamp: number;
}

interface TransferData {
    transferNumber?: string;
    agentName?: string;
    warmTransferIntroductionAgent?: string;
    warmTransferIntroductionCustomer?: string;
    specialistNotAvailableMessage?: string;
}

interface OptionalField {
    field: string;
    disqualificationRule?: {
        condition: string;
    };
    invalidResponseHandlers?: Array<{
        condition: string;
    }>;
}

interface CustomerData {
    optionalFieldsPreTransfer?: OptionalField[];
}

interface AgentPersona {
    name: string;
    tone?: string;
    humanEmulation?: boolean;
    voice?: string;
    vocabularyRestrictions?: string[];
    recordedMessageConfirmation?: string;
}

interface ScriptSection {
    type: string;
    content: string;
}

interface Script {
    start: ScriptSection[];
}

interface CampaignScriptInternal {
    id: number | string;
    type: string;
    language?: string;
    category?: string;
    title: string;
    campaign: string;
    campaign_title?: string;
    agentPersona: AgentPersona;
    customerData?: CustomerData;
    transferData?: TransferData;
    script: Script;
    objectives?: any;
}

/**
 * Campaign Configuration Manager
 * Handles loading and managing campaign scripts from various sources
 */
export class CampaignConfigManager {
    private scriptsPath: string;
    private totalCampaigns: number;
    private defaultCampaignId: number;
    private enableCustomScripts: boolean;
    private scriptCache: Map<string, CachedScript>;
    private cacheTimeout: number;

    constructor() {
        this.scriptsPath = getConfigValue('campaigns.scriptsPath') as string;
        this.totalCampaigns = getConfigValue('campaigns.totalCampaigns', 6) as number;
        this.defaultCampaignId = getConfigValue('campaigns.defaultCampaignId', 1) as number;
        this.enableCustomScripts = getConfigValue('campaigns.enableCustomScripts', false) as boolean;
        this.scriptCache = new Map();
        this.cacheTimeout = (getConfigValue('campaigns.scriptCacheTimeout', 300) as number) * 1000; // Convert to ms
    }

    /**
     * Load campaign script from file system
     */
    private loadCampaignFromFile(campaignId: number | string, type: 'outbound' | 'inbound' = 'outbound'): CampaignScriptInternal | null {
        try {
            console.log(`📂 [CAMPAIGN-CONFIG] Loading campaign from file:`, {
                campaignId,
                type,
                scriptsPath: this.scriptsPath
            });

            // PERMANENT FIX: Use correct file paths and names
            let fileName: string;
            let filePath: string;
            if (type === 'outbound') {
                fileName = `campaign${campaignId}.json`;
                filePath = path.join(process.cwd(), 'call-center-frontend', 'public', fileName);
            } else {
                fileName = `incoming-campaign${campaignId}.json`;
                filePath = path.join(process.cwd(), 'call-center-frontend', 'public', fileName);
            }
            console.log(`📂 [CAMPAIGN-CONFIG] Looking for file: ${fileName} at path: ${filePath}`);

            // Validate and sanitize the file name to prevent path traversal
            if (!fileName.match(/^[a-zA-Z0-9-]+\.json$/)) {
                logger.error(`Invalid campaign file name format: ${fileName}`);
                return null;
            }

            // Ensure the file name doesn't contain path traversal sequences
            const sanitizedFileName = path.basename(fileName);
            if (sanitizedFileName !== fileName) {
                logger.error(`Potential path traversal attempt detected: ${fileName}`);
                return null;
            }

            // Additional security check: ensure no directory traversal characters
            if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
                logger.error(`Path traversal attempt detected in filename: ${fileName}`);
                return null;
            }

            // filePath is already set above with correct path

            if (!existsSync(filePath)) {
                console.warn(`❌ [CAMPAIGN-CONFIG] Campaign file not found: ${filePath}`);
                logger.warn(`Campaign file not found: ${filePath}`);
                return null;
            }
            console.log(`✅ [CAMPAIGN-CONFIG] File exists: ${filePath}`);

            const readStartTime = Date.now();
            const fileContent = readFileSync(filePath, 'utf8');
            const readTime = Date.now() - readStartTime;
            
            const parseStartTime = Date.now();
            const campaignScript: CampaignScriptInternal = JSON.parse(fileContent);
            const parseTime = Date.now() - parseStartTime;
            
            logger.info(`⏱️ Campaign ${campaignId} (${type}) load times: read=${readTime}ms, parse=${parseTime}ms`);
            console.log(`✅ [CAMPAIGN-CONFIG] Campaign loaded successfully:`, {
                campaignId,
                type,
                title: campaignScript.campaign_title || campaignScript.title || 'Untitled',
                hasAgentPersona: !!campaignScript.agentPersona,
                hasCampaign: !!campaignScript.campaign,
                hasObjectives: !!campaignScript.objectives
            });
            
            // Apply configuration overrides
            this.applyCampaignConfigOverrides(campaignScript);
            
            return campaignScript;
        } catch (error) {
            logger.error(`Error loading campaign ${campaignId} (${type})`, error instanceof Error ? error : new Error(String(error)));
            return null;
        }
    }

    /**
     * Apply configuration overrides to campaign script
     */
    private applyCampaignConfigOverrides(campaignScript: CampaignScriptInternal): void {
        // Override transfer data with configuration
        if (campaignScript.transferData) {
            campaignScript.transferData.transferNumber = 
                process.env[`TRANSFER_NUMBER_CAMPAIGN_${campaignScript.id}`] || 
                getConfigValue('business.transfer.defaultTransferNumber') as string;
            
            campaignScript.transferData.agentName = 
                process.env[`AGENT_NAME_CAMPAIGN_${campaignScript.id}`] || 
                getConfigValue('business.transfer.defaultAgentName') as string;
        }

        // Override validation rules with configuration
        if (campaignScript.customerData?.optionalFieldsPreTransfer) {
            campaignScript.customerData.optionalFieldsPreTransfer.forEach(field => {
                if (field.field === 'vehicleCount' && field.disqualificationRule) {
                    const maxVehicles = getConfigValue('business.validation.maxVehicles', 9) as number;
                    field.disqualificationRule.condition = `value <= 0 || value >= ${maxVehicles + 1}`;
                }
                if (field.field === 'claimsCount3yrs' && field.disqualificationRule) {
                    const maxClaims = getConfigValue('business.validation.maxClaims', 3) as number;
                    field.disqualificationRule.condition = `value >= ${maxClaims}`;
                }
                if (field.field === 'vehicleYear1' && field.invalidResponseHandlers && field.invalidResponseHandlers.length > 0) {
                    const minYear = getConfigValue('business.validation.minVehicleYear', 1900) as number;
                    const maxYear = getConfigValue('business.validation.maxVehicleYear', 2027) as number;
                    if (field.invalidResponseHandlers[0]) {
                        field.invalidResponseHandlers[0].condition =
                            `response.type != 'integer' || response.value < ${minYear} || response.value > ${maxYear}`;
                    }
                }
            });
        }

        // Override vocabulary restrictions
        if (campaignScript.agentPersona) {
            campaignScript.agentPersona.vocabularyRestrictions = 
                getConfigValue('security.vocabularyRestrictions', []) as string[];
            
            campaignScript.agentPersona.recordedMessageConfirmation = 
                getConfigValue('security.recordingConfirmationMessage') as string;
        }
    }

    /**
     * Get campaign script with caching
     */
    public async getCampaignScript(
        campaignId: number | string,
        type: 'outbound' | 'inbound' = 'outbound',
        useCache: boolean = true
    ): Promise<CampaignScript | null> {
        const cacheKey = `${type}_${campaignId}`;
        console.log(`📁 [CAMPAIGN-CONFIG] Getting campaign script:`, {
            campaignId,
            type,
            useCache,
            cacheKey
        });
        
        // Check cache first
        if (useCache && this.scriptCache.has(cacheKey)) {
            const cached = this.scriptCache.get(cacheKey)!;
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                console.log(`✅ [CAMPAIGN-CONFIG] Found in cache: ${cacheKey}`);
                return cached.script;
            }
            // Cache expired, remove it
            console.log(`⚠️ [CAMPAIGN-CONFIG] Cache expired for: ${cacheKey}`);
            this.scriptCache.delete(cacheKey);
        }

        // Load from source
        let script: CampaignScriptInternal | null = null;
        
        // Try loading from file system first
        console.log(`📁 [CAMPAIGN-CONFIG] Loading from file system: ${type} campaign ${campaignId}`);
        script = this.loadCampaignFromFile(campaignId, type);
        
        // If custom scripts are enabled, try loading from database/API
        if (!script && this.enableCustomScripts) {
            console.log(`📁 [CAMPAIGN-CONFIG] No file found, trying database...`);
            script = await this.loadCampaignFromDatabase(campaignId, type);
        }
        
        // Fallback to default template
        if (!script) {
            console.log(`⚠️ [CAMPAIGN-CONFIG] No script found, using default template`);
            script = this.createDefaultCampaignTemplate(campaignId, type);
        }

        // Convert to external type
        const externalScript = script ? this.convertToExternalType(script) : null;

        // Cache the result
        if (useCache && externalScript) {
            this.scriptCache.set(cacheKey, {
                script: externalScript,
                timestamp: Date.now()
            });
        }

        return externalScript;
    }

    /**
     * Convert internal campaign script to external type
     */
    private convertToExternalType(script: CampaignScriptInternal): CampaignScript {
        return {
            id: script.id.toString(),
            name: script.title,
            content: script.campaign,
            language: script.language || 'en',
            voice: script.agentPersona?.voice,
            model: undefined, // Add model if available in your campaign scripts
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
        };
    }

    /**
     * Load campaign from database using Supabase
     */
    private async loadCampaignFromDatabase(
        campaignId: number | string,
        type: 'outbound' | 'inbound'
    ): Promise<CampaignScriptInternal | null> {
        const url = getConfigValue('auth.supabase.url') as string;
        const key = getConfigValue('auth.supabase.anonKey') as string;

        if (!url || !key) {
            logger.warn('Supabase configuration missing, skipping DB lookup');
            return null;
        }

        try {
            const client = createClient(url, key);
            const { data, error } = await client
                .from('campaign_scripts')
                .select('*')
                .eq('id', campaignId)
                .eq('type', type)
                .single();

            if (error) {
                logger.error(
                    `Error loading campaign ${campaignId} (${type}) from DB`,
                    error
                );
                return null;
            }

            if (!data) {return null;}

            const script = data as CampaignScriptInternal;
            this.applyCampaignConfigOverrides(script);
            return script;
        } catch (error) {
            logger.error(
                `Database load failed for campaign ${campaignId} (${type})`,
                error instanceof Error ? error : new Error(String(error))
            );
            return null;
        }
    }

    /**
     * Create default campaign template
     */
    private createDefaultCampaignTemplate(campaignId: number | string, type: 'outbound' | 'inbound'): CampaignScriptInternal {
        const language = getConfigValue('localization.defaultLanguage', 'en') as string;
        const defaultVoice = getConfigValue(`voices.defaultVoiceMapping.${language}.${type}`, 'Kore') as string;
        
        return {
            id: campaignId,
            type: type,
            language: language,
            category: 'general',
            title: `Campaign ${campaignId} (${type.charAt(0).toUpperCase() + type.slice(1)})`,
            campaign: `Default ${type} campaign`,
            agentPersona: {
                name: 'Agent',
                tone: 'Professional, helpful',
                humanEmulation: true,
                voice: defaultVoice,
                vocabularyRestrictions: getConfigValue('security.vocabularyRestrictions', []) as string[],
                recordedMessageConfirmation: getConfigValue('security.recordingConfirmationMessage') as string
            },
            customerData: {
                optionalFieldsPreTransfer: []
            },
            transferData: {
                transferNumber: getConfigValue('business.transfer.defaultTransferNumber') as string,
                agentName: getConfigValue('business.transfer.defaultAgentName') as string,
                warmTransferIntroductionAgent: 'Transferring customer. Ready?',
                warmTransferIntroductionCustomer: 'I will now transfer you to an agent. Please hold.',
                specialistNotAvailableMessage: 'All agents are busy. We will call you back.'
            },
            script: {
                start: [
                    { 
                        type: 'statement', 
                        content: 'Hello, how can I help you today?' 
                    }
                ]
            }
        };
    }

    /**
     * Get all available campaigns
     */
    public async getAllCampaigns(
        type: 'outbound' | 'inbound' = 'outbound'
    ): Promise<CampaignScript[]> {
        const campaigns: CampaignScript[] = [];
        for (let i = 1; i <= this.totalCampaigns; i++) {
            const campaign = await this.getCampaignScript(i, type);
            if (campaign) {
                campaigns.push(campaign);
            }
        }
        return campaigns;
    }

    /**
     * Validate campaign script structure
     */
    public validateCampaignScript(script: CampaignScriptInternal): boolean {
        const required: (keyof CampaignScriptInternal)[] = ['id', 'type', 'title', 'agentPersona', 'script'];
        for (const field of required) {
            if (!script[field]) {
                throw new Error(`Missing required field in campaign script: ${field}`);
            }
        }
        
        if (!script.agentPersona.name) {
            throw new Error('Missing agent persona name');
        }
        
        if (!script.script.start || !Array.isArray(script.script.start)) {
            throw new Error('Invalid script structure: missing or invalid start section');
        }
        
        return true;
    }

    /**
     * Clear cache
     */
    public clearCache(): void {
        this.scriptCache.clear();
        logger.info('Campaign script cache cleared');
    }

    /**
     * Invalidate specific cache entry
     */
    public invalidateCache(campaignId: number | string, type: 'outbound' | 'inbound' = 'outbound'): boolean {
        const cacheKey = `${type}_${campaignId}`;
        const deleted = this.scriptCache.delete(cacheKey);
        if (deleted) {
            logger.info(`Cache invalidated for ${cacheKey}`);
        }
        return deleted;
    }

    /**
     * Invalidate all cache entries for a specific campaign ID
     */
    public invalidateCampaignCache(campaignId: number | string): boolean {
        const outboundKey = `outbound_${campaignId}`;
        const incomingKey = `incoming_${campaignId}`;

        const deletedOutbound = this.scriptCache.delete(outboundKey);
        const deletedIncoming = this.scriptCache.delete(incomingKey);

        if (deletedOutbound || deletedIncoming) {
            logger.info(`Cache invalidated for campaign ${campaignId} (outbound: ${deletedOutbound}, incoming: ${deletedIncoming})`);
        }

        return deletedOutbound || deletedIncoming;
    }

    /**
     * Get cache statistics
     */
    public getCacheStats(): {
        size: number;
        keys: string[];
        timeout: number;
        maxSize: number;
    } {
        return {
            size: this.scriptCache.size,
            keys: Array.from(this.scriptCache.keys()),
            timeout: this.cacheTimeout,
            maxSize: 1000 // Add max size info
        };
    }
}

// Export singleton instance
export const campaignConfigManager = new CampaignConfigManager();

// Export utility functions
export async function getCampaignScript(
    campaignId: number | string,
    type: 'outbound' | 'inbound' = 'outbound'
): Promise<CampaignScript | null> {
    return campaignConfigManager.getCampaignScript(campaignId, type);
}

export async function getAllCampaigns(
    type: 'outbound' | 'inbound' = 'outbound'
): Promise<CampaignScript[]> {
    return campaignConfigManager.getAllCampaigns(type);
}

export function validateCampaignScript(script: CampaignScriptInternal): boolean {
    return campaignConfigManager.validateCampaignScript(script);
}