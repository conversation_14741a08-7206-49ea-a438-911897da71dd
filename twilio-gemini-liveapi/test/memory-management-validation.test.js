import { test } from 'node:test';
import assert from 'node:assert';
import { standardizeConnectionData, cleanupConnectionData } from '../src/utils/websocket-utils';

test('Memory Management Validation', async (t) => {
    await t.test('Bounded arrays are properly initialized', () => {
        const connectionData = {};
        const mockWs = { readyState: 1 };
        
        const standardized = standardizeConnectionData(connectionData, mockWs, 'twilio_call');
        
        // Check that arrays are initialized
        assert.ok(Array.isArray(standardized.conversationLog));
        assert.ok(Array.isArray(standardized.fullTranscript));
        assert.ok(Array.isArray(standardized.speechTranscript));
        
        // Check that size limits are set
        assert.strictEqual(standardized.maxConversationLogSize, 500);
        assert.strictEqual(standardized.maxTranscriptSize, 1000);
        assert.strictEqual(standardized.maxSpeechTranscriptSize, 1000);
    });

    await t.test('Conversation log respects size limits', () => {
        const connectionData = {
            conversationLog: [],
            maxConversationLogSize: 3
        };
        
        // Add entries beyond the limit
        for (let i = 0; i < 5; i++) {
            connectionData.conversationLog.push({
                role: 'user',
                content: `Message ${i}`,
                timestamp: Date.now()
            });
            
            // Simulate the trimming logic from session-manager.js
            if (connectionData.conversationLog.length > connectionData.maxConversationLogSize) {
                connectionData.conversationLog.splice(0, connectionData.conversationLog.length - connectionData.maxConversationLogSize);
            }
        }
        
        // Should only have the last 3 entries
        assert.strictEqual(connectionData.conversationLog.length, 3);
        assert.strictEqual(connectionData.conversationLog[0].content, 'Message 2');
        assert.strictEqual(connectionData.conversationLog[2].content, 'Message 4');
    });

    await t.test('Full transcript respects size limits', () => {
        const connectionData = {
            fullTranscript: [],
            maxTranscriptSize: 2
        };
        
        // Add entries beyond the limit
        for (let i = 0; i < 4; i++) {
            connectionData.fullTranscript.push({
                role: 'assistant',
                content: `Response ${i}`,
                timestamp: Date.now()
            });
            
            // Simulate the trimming logic from transcription-manager.js
            if (connectionData.fullTranscript.length > connectionData.maxTranscriptSize) {
                connectionData.fullTranscript.splice(0, connectionData.fullTranscript.length - connectionData.maxTranscriptSize);
            }
        }
        
        // Should only have the last 2 entries
        assert.strictEqual(connectionData.fullTranscript.length, 2);
        assert.strictEqual(connectionData.fullTranscript[0].content, 'Response 2');
        assert.strictEqual(connectionData.fullTranscript[1].content, 'Response 3');
    });

    await t.test('Cleanup properly clears arrays', () => {
        const connectionData = {
            conversationLog: [{ content: 'test1' }, { content: 'test2' }],
            fullTranscript: [{ content: 'transcript1' }],
            speechTranscript: [{ content: 'speech1' }],
            ws: { readyState: 1, close: () => {} }
        };
        
        cleanupConnectionData(connectionData);
        
        // Arrays should be cleared
        assert.strictEqual(connectionData.conversationLog.length, 0);
        assert.strictEqual(connectionData.fullTranscript.length, 0);
        assert.strictEqual(connectionData.speechTranscript.length, 0);
    });

    await t.test('Memory usage stays bounded under load', () => {
        const connectionData = {
            conversationLog: [],
            fullTranscript: [],
            speechTranscript: [],
            maxConversationLogSize: 10,
            maxTranscriptSize: 10,
            maxSpeechTranscriptSize: 10
        };
        
        // Simulate heavy usage
        for (let i = 0; i < 100; i++) {
            // Add to conversation log
            connectionData.conversationLog.push({
                role: i % 2 === 0 ? 'user' : 'assistant',
                content: `Message ${i}`,
                timestamp: Date.now()
            });
            
            // Add to transcript
            connectionData.fullTranscript.push({
                role: 'user',
                content: `Transcript ${i}`,
                timestamp: Date.now()
            });
            
            // Add to speech transcript
            connectionData.speechTranscript.push({
                role: 'user',
                content: `Speech ${i}`,
                timestamp: Date.now()
            });
            
            // Simulate trimming (as done in the actual code)
            if (connectionData.conversationLog.length > connectionData.maxConversationLogSize) {
                connectionData.conversationLog.splice(0, connectionData.conversationLog.length - connectionData.maxConversationLogSize);
            }
            
            if (connectionData.fullTranscript.length > connectionData.maxTranscriptSize) {
                connectionData.fullTranscript.splice(0, connectionData.fullTranscript.length - connectionData.maxTranscriptSize);
            }
            
            if (connectionData.speechTranscript.length > connectionData.maxSpeechTranscriptSize) {
                connectionData.speechTranscript.splice(0, connectionData.speechTranscript.length - connectionData.maxSpeechTranscriptSize);
            }
        }
        
        // All arrays should be at their maximum size
        assert.strictEqual(connectionData.conversationLog.length, 10);
        assert.strictEqual(connectionData.fullTranscript.length, 10);
        assert.strictEqual(connectionData.speechTranscript.length, 10);
        
        // Should contain the most recent entries
        assert.strictEqual(connectionData.conversationLog[9].content, 'Message 99');
        assert.strictEqual(connectionData.fullTranscript[9].content, 'Transcript 99');
        assert.strictEqual(connectionData.speechTranscript[9].content, 'Speech 99');
    });

    await t.test('BoundedMap functionality from session manager', () => {
        // Test the BoundedMap class used in session manager
        class BoundedMap extends Map {
            constructor(maxSize = 1000) {
                super();
                this.maxSize = maxSize;
            }

            set(key, value) {
                if (this.size >= this.maxSize && !this.has(key)) {
                    const firstKey = this.keys().next().value;
                    this.delete(firstKey);
                }
                return super.set(key, value);
            }
        }
        
        const boundedMap = new BoundedMap(3);
        
        // Add entries beyond the limit
        boundedMap.set('key1', 'value1');
        boundedMap.set('key2', 'value2');
        boundedMap.set('key3', 'value3');
        boundedMap.set('key4', 'value4'); // Should remove key1
        
        assert.strictEqual(boundedMap.size, 3);
        assert.strictEqual(boundedMap.has('key1'), false);
        assert.strictEqual(boundedMap.has('key4'), true);
    });
});
