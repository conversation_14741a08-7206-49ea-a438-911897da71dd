import { describe, it } from 'node:test';
import assert from 'node:assert';
import { generateLocalSummary } from '../src/session/summary-generator';

describe('SummaryGenerator', () => {
    it('creates a basic summary from conversation log', () => {
        const log = [
            { role: 'user', content: 'Hi', timestamp: Date.now() - 1000 },
            { role: 'assistant', content: 'Hello', timestamp: Date.now() }
        ];
        const summary = generateLocalSummary(log, '');
        assert.ok(summary.includes('Total messages: 2'));
        assert.ok(summary.includes('Hi'));
    });
});
