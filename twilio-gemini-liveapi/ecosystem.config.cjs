module.exports = {
    apps: [
        {
            name: 'twilio-gemini-backend',
            script: 'npm',
            args: 'run start:ts',
            env_file: '.env',
            env: {
                PORT: 3101,
                NODE_ENV: 'production',
                PUBLIC_URL: 'https://gemini-api.verduona.com',
                CORS_ORIGIN: 'https://twilio-gemini.verduona.com',
                GEMINI_API_KEY: 'AIzaSyCaFEQUOGqa9HrhU0gqhl1wNuoOwcpodAg',
                TWILIO_ACCOUNT_SID: '**********************************',
                TWILIO_AUTH_TOKEN: '8e2cee6f53d27a8dc7f4ef883af228ef',
                TWILIO_PHONE_NUMBER: '+***********',
                VOICE_AOEDE: '<PERSON><PERSON><PERSON>, female, bright neutral narrator',
                VOICE_PUCK: 'Puck, male, lively higher tenor',
                VOICE_CHARON: '<PERSON><PERSON>, male, deep warm baritone',
                VOICE_KORE: '<PERSON><PERSON>, female, soft alto empathetic',
                VOICE_FENRIR: '<PERSON><PERSON><PERSON>, male, assertive mid-range',
                VOICE_LEDA: 'Leda, female, clear RP-style announcer',
                VOICE_ORUS: 'Orus, male, relaxed breathy tenor',
                VOICE_ZEPHYR: 'Zephyr, female, airy youthful soprano'
            }
        },
        {
            name: 'twilio-gemini-frontend',
            cwd: './call-center-frontend',
            script: 'npm',
            args: 'start',
            env: {
                PORT: 3011,
                NODE_ENV: 'production',
                NEXT_PUBLIC_BACKEND_URL: 'https://gemini-api.verduona.com'
            }
        }
    ]
};
