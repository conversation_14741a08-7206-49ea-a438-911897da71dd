# Twilio Gemini Live API Demo

This project integrates Twilio with Google's Gemini Live API for voice interactions. It includes a backend Node.js server and a Next.js frontend.

Development follows the **Cline Rules** documented in [`.clinerules`](./.clinerules). These rules require running `npm run lint:fix` and `npm test` before committing any changes.

For a detailed overview of the modular architecture, see [README-MODULAR.md](README-MODULAR.md).

## Centralized Development Setup (PM2)

This demo, along with other related projects (`verduona-web-2025`, `gemini-2-live-api-demo`, `twilio-gemini-operator-demo`), is managed for development using a central PM2 configuration file located at `/home/<USER>/github/ecosystem.config.js`.

This central configuration starts the following processes relevant to this demo:
- **`gemini-backend-dev`**: The Node.js backend server (from `./twilio-gemini-liveapi/`). Listens internally on port 3101.
- **`gemini-frontend-dev`**: The Next.js frontend development server (from `./twilio-gemini-liveapi/call-center-frontend/`). Listens internally on port 3011.

**To start/stop/manage these development processes:**

```bash
# Navigate to the root directory containing the central config
cd /home/<USER>/github

# Start all defined development processes
pm2 start ecosystem.config.js

# Check status
pm2 status

# View logs for this backend
pm2 logs gemini-backend-dev

# View logs for this frontend
pm2 logs gemini-frontend-dev

# Stop all processes defined in the config
pm2 stop ecosystem.config.js

# Restart all processes defined in the config
pm2 restart ecosystem.config.js

# Delete all processes defined in the config from PM2
pm2 delete ecosystem.config.js
```

**Important Note on Applying Changes:** While `pm2 restart ecosystem.config.js` or `pm2 reload ecosystem.config.js` *should* apply changes, we've observed inconsistencies. To reliably apply changes made to the code (e.g., `index.js`) or environment variables (`.env` files) for `oai-backend-dev` or `oai-frontend-dev`, it is recommended to use a manual restart for the specific process:
```bash
pm2 restart oai-backend-dev
# or
pm2 restart oai-frontend-dev
```

**Note:** The `ecosystem.config.js` relies on `.env` files within the respective project directories (`./twilio-openai-realapi/.env` and `./twilio-openai-realapi/call-center-frontend/.env`) for specific configurations like API keys and URLs.

## Accessing the Application (Development via Nginx Proxy)

An Nginx reverse proxy is configured (see `/etc/nginx/sites-available/verduona.com`) to route requests from the public domain to the internal ports managed by PM2.

- **Frontend:** Accessed via `https://www.verduona.com/oai-demo/` (routes to internal port 3000)
- **Backend API:** Accessed via `https://www.verduona.com/oai-api/` (routes to internal port 3001)
- **Twilio Callbacks/WebSocket:** Twilio connects directly to the backend via `https://www.verduona.com/` (routes specific paths like `/incoming-call`, `/media-stream` to internal port 3001). The backend's `PUBLIC_URL` environment variable must be set to `https://www.verduona.com`.

## Environment Variables

Ensure the following `.env` files are correctly configured:

**`./twilio-gemini-liveapi/.env` (Backend)**
```dotenv
GEMINI_API_KEY=your_gemini_key
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number # Used as 'from' number
PORT=3101 # Internal port
PUBLIC_URL=https://gemini-api.verduona.com # Base URL for Twilio callbacks/WS
CORS_ORIGIN=https://gemini-api.verduona.com # Allowed origin for browser requests (if handled by proxy)
# ... other settings ...
```

**`./twilio-gemini-liveapi/call-center-frontend/.env` (Frontend)**
```dotenv
NEXT_PUBLIC_BACKEND_URL=https://gemini-api.verduona.com # URL for frontend to call backend API
```

## Local Standalone Scripts (Deprecated - Use Central PM2 Setup)

*The following scripts (`start.sh`, `stop.sh`, `health-check.sh`, `debug.sh`) exist but are likely superseded by the central PM2 management described above.*

## Prerequisites

- Node.js 18.x or higher
- PM2 for process management
- Valid Twilio and Gemini API credentials

## Local Setup

1. Create a `.env` file with your credentials (use `.env.example` as a template). Ensure URLs match the intended setup (e.g., `PUBLIC_URL=https://your-ngrok-or-public-domain`).

2. Start the application using PM2: `pm2 start ecosystem.config.cjs`

3. Stop the application: `pm2 stop all`

## Data Storage

- `./rec`: Stores recorded call audio files
- `./conversations`: Stores call transcriptions
- `./data`: Stores extracted information from calls

## Project Structure

The project follows a clean, modular architecture:

```
twilio-gemini-liveapi/
├── src/                    # Source code modules
│   ├── api/               # API route handlers
│   ├── audio/             # Audio processing utilities
│   ├── config/            # Configuration management
│   ├── context/           # Conversation context handling
│   ├── gemini/            # Gemini API integration
│   ├── middleware/        # Express middleware
│   ├── scripts/           # Campaign script management
│   ├── session/           # Session management
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Utility functions
│   └── websocket/         # WebSocket handling
├── call-center-frontend/  # React/Next.js frontend
├── test/                  # Test files
├── docs/                  # Documentation
├── public/                # Static assets
├── middleware/            # Legacy middleware (being migrated to src/)
├── index.js               # Main server entry point
├── package.json           # Dependencies and scripts
└── README.md              # This file
```

## Repository Cleanup

This repository has been cleaned up to remove:
- Loose documentation files (various .md reports and guides)
- Test and debug files scattered in the root
- Data files and logs that shouldn't be in version control
- Temporary and legacy files

The `.gitignore` file has been updated to prevent these types of files from being committed in the future.

## Local Testing Message Flow

When connecting to `/local-audio-session` for manual testing, use these message types:
`start-session`, `audio-data`, `text-message`, `turn-complete`, `end-session`.

## License

This project is licensed under the MIT License. See [LICENSE](LICENSE) for details.
